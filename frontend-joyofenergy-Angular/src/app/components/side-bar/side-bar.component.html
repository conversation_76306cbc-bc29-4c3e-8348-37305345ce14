<div class="p3 menuWidth overflow-auto">
  <div class="energy-card" *ngFor="let energyField of energyConsumptionMap | keyvalue:asIsOrder">
    <div class="h2 greyBlue">
      {{ energyField.key }}
    </div>
    <div class="darkgray mb2">
      {{ energyField.value }}
    </div>
  </div>
  <section class="h5 darkgray mb2">


    <h4 class="h4 mb1">Your devices:</h4>
    <div class="shadow-2 roundedMore bg-super-light-grey mb1" *ngFor="let device of devicesMap | keyvalue">
      <p class="darkgray pl2 pt1 pb1">{{ device.key }}</p>
      <p class="h5 darkgray bold pl2 pb1 pt1 bg-very-light-grey">
        {{ device.value }}
      </p>
    </div>
  </section>
</div>